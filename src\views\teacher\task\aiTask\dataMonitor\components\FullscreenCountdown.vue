<script setup lang="ts">
/**
 * 全屏状态下的倒计时组件
 */

interface Props {
  /** 倒计时时间（毫秒） */
  time: number
  /** 时间剩余百分比 */
  timePercentage: number
  /** 任务是否已完成 */
  isTaskCompleted: boolean
  /** 任务是否已开始 */
  isTaskStarted: boolean
  /** 倒计时颜色类配置 */
  countdownColorClass: {
    colorClass?: string
    iconSrc?: string
  }
}

interface Emits {
  (e: 'change', timeData: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

/**
 * 处理倒计时变化
 */
function handleCountdownChange(timeData: any) {
  emit('change', timeData)
}
</script>

<template>
  <div class="absolute top-12px left-1/2 transform -translate-x-1/2 z-10">
    <div
      class="flex items-center px-12px py-6px rounded-[20px] text-14px font-500 text-[#333]"
      :class="[
        timePercentage >= 50
          ? 'bg-[#F4FFF4] border border-[#5BE361]'
          : timePercentage > 10
            ? 'bg-[#FFFBE6] border border-[#FFE58F]'
            : 'bg-[#FFF0F0] border border-[#F92A2A]',
      ]"
    >
      <img
        v-if="!isTaskCompleted && countdownColorClass.iconSrc"
        class="w-16px h-16px mr-6px"
        :src="countdownColorClass.iconSrc"
        alt=""
      />
      <template v-if="isTaskCompleted">
        课程已结束
      </template>
      <template v-else-if="!isTaskStarted">
        课程将于
        <van-count-down
          :time="time"
          format="mm:ss"
          class="mx-4px"
          @change="handleCountdownChange"
        />
        后开始
      </template>
      <template v-else>
        课程将于
        <van-count-down
          :time="time"
          format="mm:ss"
          class="mx-4px"
          @change="handleCountdownChange"
        />
        后结束
      </template>
    </div>
  </div>
</template>
